import type { PocketOption } from '../PocketOption'

export class SelectAsset {
	private static broker: PocketOption

	static init(broker: PocketOption) {
		this.broker = broker
	}

	/**
	 *
	 * @param assetSymbol The asset symbol to change to
	 * @param interval The interval if the chart timeframe. Can be 30 or 'S30'
	 * @returns
	 */
	static async call(assetSymbol: string, interval: string | number): Promise<void> {
		return new Promise<void>((resolve, _reject) => {
			this.broker.emit('changeSymbol', { asset: assetSymbol, period: interval })
			resolve()
		})
	}
}
