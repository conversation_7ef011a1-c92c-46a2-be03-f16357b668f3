import { io, type Socket } from 'socket.io-client'
import { Region } from '../constants'
import { extractAuth } from '../utils/parsers'
import { logger } from '../utils/logger'
import { formatData } from '../utils/formatter'
import { Order } from './channels/order'
import { Candles } from './channels/candles'

export class PocketOption {
	private ws?: Socket
	private balance: number = 0
	private isConnected: boolean = false
	private connectionTimeout: number = 10000 // 10 seconds
	private heartbeatInterval: NodeJS.Timeout | null = null

	private orderPayload: OrderPayload | null = null
	private orderResult: OrderResult | null = null
	private chartSettings: ChartSettings | null = null

	constructor(private ssID: string, private demo: boolean = true) {}

	connect(): Promise<void> {
		return new Promise((resolve, reject) => {
			const endpoint = this.demo ? Region.DEMO_REGION : Region.getRegion()[0]
			const options = Region.SOCKET_OPTIONS
			const auth = extractAuth(this.ssID)

			if (!auth) {
				return reject(new Error('Invalid ssID, authentication failed.'))
			}

			const { session, uid } = auth

			// Set connection timeout
			const timeout = setTimeout(() => {
				if (this.ws) {
					this.ws.disconnect()
				}
				reject(new Error('Connection timeout'))
			}, this.connectionTimeout)

			this.ws = io(endpoint, options)

			this.ws.once('connect', () => {
				if (this.ws) {
					this.ws.emit('auth', {
						isDemo: this.demo ? 1 : 0,
						isFastHistory: true,
						platform: 2,
						session,
						uid
					})
				}
			})

			this.ws.once('successauth', () => {
				clearTimeout(timeout)
				this.isConnected = true
				logger.success(`Broker`, `Authenticated successfully`)

				const candles = Candles.call('BTCUSD')

				// Set up balance update listener
				this.setupListeners()

				// Start heartbeat
				this.startHeartbeat()

				resolve()
			})

			this.ws.onAny((event: string, ...args: unknown[]) => {
				logger.debug(`Broker`, `Received event: ${event}`)
			})

			this.ws.on('disconnect', () => {
				this.disconnect()
				logger.warn(`Broker`, `Disconnected from server`)
			})

			this.ws.on('error', (err: Error) => {
				clearTimeout(timeout)
				this.isConnected = false
				logger.error(`Broker`, `Error`, err)
				reject(err)
			})

			this.ws.on('connect_error', (err: Error) => {
				clearTimeout(timeout)
				this.isConnected = false
				logger.error(`Broker`, `Connection error`, err)
				reject(err)
			})
		})
	}

	async emit(event: string, data: unknown): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.emit(event, data)
			resolve()
		})
	}

	async on(event: string, callback: (data: unknown[]) => void): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.on(event, (data: unknown[]) => {
				const parsedData = formatData(data) as unknown[]
				callback(parsedData)
			})
			resolve()
		})
	}

	async once(event: string, callback: (data: unknown[]) => void): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.once(event, (data: unknown[]) => {
				const parsedData = formatData(data) as unknown[]
				callback(parsedData)
				resolve()
			})
		})
	}

	async getBalance(): Promise<number> {
		if (!this.ws || !this.isConnected) {
			throw new Error('Not connected to server')
		}

		return new Promise((resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			resolve(this.balance)
		})
	}

	async placeOrder(payload: OrderPayload): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			const order = Order.call(payload)

			resolve()
		})
	}

	/**
	 * Check if the broker is connected
	 */
	getConnectionStatus(): boolean {
		return this.isConnected && this.ws?.connected === true
	}

	getChartSettings(): ChartSettings | null {
		return this.chartSettings
	}

	/**
	 * Disconnect from the broker
	 */
	disconnect(): void {
		if (this.ws) {
			this.stopHeartbeat()
			this.ws.disconnect()
			this.isConnected = false
			logger.info('Broker', 'Disconnected from server')
		}
	}

	/**
	 * Set up listener for balance updates
	 */
	private setupListeners(): void {
		if (!this.ws) return

		// Listen for balance updates from the server
		this.ws.on('successupdateBalance', (data: unknown) => {
			const parsedData = formatData(data) as { isDemo: number; balance: number }

			if (parsedData && typeof parsedData.balance === 'number') {
				const oldBalance = this.balance
				this.balance = parsedData.balance
				logger.info('Broker', `Balance updated: ${oldBalance} -> ${this.balance}`)
			}
		})

		this.ws.on('updateCharts', this.handleChartSettings)
	}

	private handleChartSettings = (data: unknown[]): void => {
		const parsedData = formatData(data) as ChartsData
		if (parsedData && typeof parsedData.settings === 'object') {
			this.chartSettings = parsedData.settings as ChartSettings
		} else if (parsedData && typeof parsedData.settings === 'string') {
			this.chartSettings = JSON.parse(parsedData.settings) as ChartSettings
		}
	}

	private startHeartbeat(): void {
		if (!this.ws) return

		// Send a ping every 20 seconds
		this.heartbeatInterval = setInterval(() => {
			if (this.ws) {
				this.ws.emit('ps')
			}
		}, 20000)
	}

	private stopHeartbeat(): void {
		if (!this.heartbeatInterval) return

		clearInterval(this.heartbeatInterval)
		this.heartbeatInterval = null
	}
}
